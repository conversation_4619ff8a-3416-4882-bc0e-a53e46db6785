package com.example.onelinediary.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Custom colors for OneLineDiary - Standard Theme (Green + Yellow Flowers)
val BackgroundGreen = Color(0xFFB0C088) // Darker pastel green background
val ButtonGreen = Color(0xFF8A9A62)     // Darker green for buttons (darker tint of background)
val TextBrown = Color(0xFF3E2723)       // Dark brown for text
val AccentGold = Color(0xFFB8924A)      // Darker golden accent color (kept for backward compatibility)

// Black and White Theme Colors
val BackgroundWhite = Color(0xFFE0E0E0) // Darker background for black and white theme
val ButtonGray = Color(0xFFA0A0A0)      // Darker gray for buttons (darker tint of background)
val TextBlack = Color(0xFF212121)       // Dark text for black and white theme
val AccentGray = Color(0xFF606060)      // Darker neutral gray accent for black and white theme

// Additional custom background colors - darker pastel tones
val BackgroundBlue = Color(0xFFC8D8E8)      // Darker pastel blue background
val BackgroundPink = Color(0xFFE8D0DA)      // Darker pastel pink background
val BackgroundPurple = Color(0xFFD8D0E8)    // Darker pastel purple background
val BackgroundYellow = Color(0xFFE8E0C8)    // Darker pastel yellow background
val BackgroundOrange = Color(0xFFE8D8C8)    // Darker pastel orange background
val BackgroundRed = Color(0xFFE8D0D0)       // Darker pastel red background
val BackgroundTeal = Color(0xFFC8E8E8)      // Darker pastel teal background
val BackgroundLavender = Color(0xFFE0D0E8)  // Darker pastel lavender background

// Darker button colors (darker tints of background colors)
val ButtonBlue = Color(0xFFA0C0D8)          // Darker blue for buttons
val ButtonPink = Color(0xFFD8A0C0)          // Darker pink for buttons
val ButtonPurple = Color(0xFFC0A0D8)        // Darker purple for buttons
val ButtonYellow = Color(0xFFD8D0A0)        // Darker yellow for buttons
val ButtonOrange = Color(0xFFD8C0A0)        // Darker orange for buttons
val ButtonRed = Color(0xFFD8A0A0)           // Darker red for buttons
val ButtonTeal = Color(0xFFA0D8D8)          // Darker teal for buttons
val ButtonLavender = Color(0xFFC8A0D8)      // Darker lavender for buttons

// Lighter tint colors for selected/chosen button states (moderately light versions)
val LightButtonGreen = Color(0xFFC8D0B8)    // Moderately light green for selected states
val LightButtonGray = Color(0xFFD8D8D8)     // Moderately light gray for selected states
val LightButtonBlue = Color(0xFFC8D8E8)     // Moderately light blue for selected states
val LightButtonPink = Color(0xFFE8C8D8)     // Moderately light pink for selected states
val LightButtonPurple = Color(0xFFD8C8E8)   // Moderately light purple for selected states
val LightButtonYellow = Color(0xFFE8E0C8)   // Moderately light yellow for selected states
val LightButtonOrange = Color(0xFFE8D8C8)   // Moderately light orange for selected states
val LightButtonRed = Color(0xFFE8C8C8)      // Moderately light red for selected states
val LightButtonTeal = Color(0xFFC8E8E8)     // Moderately light teal for selected states
val LightButtonLavender = Color(0xFFE0C8E8) // Moderately light lavender for selected states

// Soft pastel colors for disabled button states (darker muted versions)
val SoftPastelGreen = Color(0xFFC0D0B0)     // Darker muted green for disabled states
val SoftPastelGray = Color(0xFFD0D0D0)      // Darker muted gray for disabled states
val SoftPastelBlue = Color(0xFFB8C8D8)      // Darker muted blue for disabled states
val SoftPastelPink = Color(0xFFD8B8C8)      // Darker muted pink for disabled states
val SoftPastelPurple = Color(0xFFC8B8D8)    // Darker muted purple for disabled states
val SoftPastelYellow = Color(0xFFD8D0B8)    // Darker muted yellow for disabled states
val SoftPastelOrange = Color(0xFFD8C8B8)    // Darker muted orange for disabled states
val SoftPastelRed = Color(0xFFD8B8B8)       // Darker muted red for disabled states
val SoftPastelTeal = Color(0xFFB8D8D8)      // Darker muted teal for disabled states
val SoftPastelLavender = Color(0xFFD0B8D8)  // Darker muted lavender for disabled states

// Soft pastel complementary colors for disabled button backgrounds (darker muted complementary colors)
val SoftComplementaryGreen = Color(0xFFD0C0C8)     // Darker reddish-purple (complement of green)
val SoftComplementaryGray = Color(0xFFC8C8C8)      // Darker gray (neutral complement)
val SoftComplementaryBlue = Color(0xFFD8C8B8)      // Darker orange (complement of blue)
val SoftComplementaryPink = Color(0xFFB8D8C8)      // Darker green (complement of pink)
val SoftComplementaryPurple = Color(0xFFD8D0B8)    // Darker yellow (complement of purple)
val SoftComplementaryYellow = Color(0xFFC8B8D8)    // Darker purple (complement of yellow)
val SoftComplementaryOrange = Color(0xFFB8C8D8)    // Darker blue (complement of orange)
val SoftComplementaryRed = Color(0xFFB8D8D8)       // Darker cyan (complement of red)
val SoftComplementaryTeal = Color(0xFFD8B8B8)      // Darker red (complement of teal)
val SoftComplementaryLavender = Color(0xFFC8D8B8)  // Darker lime (complement of lavender)

// Theme enum class to manage theme selection
enum class AppTheme {
    STANDARD,    // Green with yellow flowers (default)
    BLACK_WHITE, // Black and white theme
    BLUE,        // Blue background theme
    PINK,        // Pink background theme
    PURPLE,      // Purple background theme
    YELLOW,      // Yellow background theme
    ORANGE,      // Orange background theme
    RED,         // Red background theme
    TEAL,        // Teal background theme
    LAVENDER     // Lavender background theme
}

// Decoration theme enum class to manage background decorations
enum class DecorationTheme {
    NONE,        // No decorations (default)
    STANDARD,    // Standard decorations (same as none for now)
    ROMANTIC     // Romantic decorations with flowers and hearts
}