package com.example.onelinediary.components

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.AccentGold
import com.example.onelinediary.ui.theme.ButtonGreen
import com.example.onelinediary.ui.theme.TextBrown
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.ui.theme.AppTheme
import com.example.onelinediary.ui.theme.LightButtonGreen
import com.example.onelinediary.ui.theme.LightButtonGray
import com.example.onelinediary.ui.theme.LightButtonBlue
import com.example.onelinediary.ui.theme.LightButtonPink
import com.example.onelinediary.ui.theme.LightButtonPurple
import com.example.onelinediary.ui.theme.LightButtonYellow
import com.example.onelinediary.ui.theme.LightButtonOrange
import com.example.onelinediary.ui.theme.LightButtonRed
import com.example.onelinediary.ui.theme.LightButtonTeal
import com.example.onelinediary.ui.theme.LightButtonLavender
import com.example.onelinediary.ui.theme.SoftPastelGreen
import com.example.onelinediary.ui.theme.SoftPastelGray
import com.example.onelinediary.ui.theme.SoftPastelBlue
import com.example.onelinediary.ui.theme.SoftPastelPink
import com.example.onelinediary.ui.theme.SoftPastelPurple
import com.example.onelinediary.ui.theme.SoftPastelYellow
import com.example.onelinediary.ui.theme.SoftPastelOrange
import com.example.onelinediary.ui.theme.SoftPastelRed
import com.example.onelinediary.ui.theme.SoftPastelTeal
import com.example.onelinediary.ui.theme.SoftPastelLavender
import com.example.onelinediary.ui.theme.SoftComplementaryGreen
import com.example.onelinediary.ui.theme.SoftComplementaryGray
import com.example.onelinediary.ui.theme.SoftComplementaryBlue
import com.example.onelinediary.ui.theme.SoftComplementaryPink
import com.example.onelinediary.ui.theme.SoftComplementaryPurple
import com.example.onelinediary.ui.theme.SoftComplementaryYellow
import com.example.onelinediary.ui.theme.SoftComplementaryOrange
import com.example.onelinediary.ui.theme.SoftComplementaryRed
import com.example.onelinediary.ui.theme.SoftComplementaryTeal
import com.example.onelinediary.ui.theme.SoftComplementaryLavender

// Common colors used across the app - these will be theme-aware when used in composables

/**
 * Get the appropriate lighter tint color based on the current theme
 */
@Composable
fun getLighterTintColor(): Color {
    return when (LocalAppTheme.current) {
        AppTheme.BLACK_WHITE -> LightButtonGray
        AppTheme.STANDARD -> LightButtonGreen
        AppTheme.BLUE -> LightButtonBlue
        AppTheme.PINK -> LightButtonPink
        AppTheme.PURPLE -> LightButtonPurple
        AppTheme.YELLOW -> LightButtonYellow
        AppTheme.ORANGE -> LightButtonOrange
        AppTheme.RED -> LightButtonRed
        AppTheme.TEAL -> LightButtonTeal
        AppTheme.LAVENDER -> LightButtonLavender
    }
}

/**
 * Get the appropriate soft pastel color based on the current theme for disabled states
 */
@Composable
fun getSoftPastelColor(): Color {
    return when (LocalAppTheme.current) {
        AppTheme.BLACK_WHITE -> SoftPastelGray
        AppTheme.STANDARD -> SoftPastelGreen
        AppTheme.BLUE -> SoftPastelBlue
        AppTheme.PINK -> SoftPastelPink
        AppTheme.PURPLE -> SoftPastelPurple
        AppTheme.YELLOW -> SoftPastelYellow
        AppTheme.ORANGE -> SoftPastelOrange
        AppTheme.RED -> SoftPastelRed
        AppTheme.TEAL -> SoftPastelTeal
        AppTheme.LAVENDER -> SoftPastelLavender
    }
}

/**
 * Get the appropriate soft complementary background color for disabled states
 */
@Composable
fun getSoftComplementaryBackgroundColor(): Color {
    return when (LocalAppTheme.current) {
        AppTheme.BLACK_WHITE -> SoftComplementaryGray
        AppTheme.STANDARD -> SoftComplementaryGreen
        AppTheme.BLUE -> SoftComplementaryBlue
        AppTheme.PINK -> SoftComplementaryPink
        AppTheme.PURPLE -> SoftComplementaryPurple
        AppTheme.YELLOW -> SoftComplementaryYellow
        AppTheme.ORANGE -> SoftComplementaryOrange
        AppTheme.RED -> SoftComplementaryRed
        AppTheme.TEAL -> SoftComplementaryTeal
        AppTheme.LAVENDER -> SoftComplementaryLavender
    }
}

/**
 * Get the appropriate darker background color based on the current theme
 */
@Composable
fun getDarkerBackgroundColor(): Color {
    return MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f)
}

/**
 * Standard button colors for the app
 */
@Composable
fun standardButtonColors() = ButtonDefaults.buttonColors(
    containerColor = MaterialTheme.colorScheme.primary,
    contentColor = MaterialTheme.colorScheme.onPrimary,
    disabledContainerColor = getSoftComplementaryBackgroundColor(), // Soft complementary background for disabled states
    disabledContentColor = getSoftPastelColor() // Soft pastel color for disabled states
)

/**
 * Standard outlined button colors for the app - using ButtonDefaults since OutlinedButtonDefaults might not be available
 */
@Composable
fun standardOutlinedButtonColors(isSelected: Boolean = false) = ButtonDefaults.outlinedButtonColors(
    containerColor = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surface, // Opaque background
    contentColor = MaterialTheme.colorScheme.onBackground,
    disabledContainerColor = getSoftComplementaryBackgroundColor(), // Soft complementary background for disabled states
    disabledContentColor = getSoftPastelColor() // Soft pastel color for disabled states
)

/**
 * Standard button modifier for consistent styling - borderless
 */
@Composable
fun standardButtonModifier(
    baseModifier: Modifier = Modifier,
    isDisabled: Boolean = false,
    isSelected: Boolean = false
): Modifier {
    // Remove border for cleaner visual appearance like back button
    return baseModifier
}

/**
 * Standard content padding for buttons
 */
val standardButtonPadding = PaddingValues(8.dp)

/**
 * Standard button shape
 */
val standardButtonShape = RoundedCornerShape(12.dp)

/**
 * Back button colors that match enabled button styling based on current theme
 */
@Composable
fun backButtonColors() = ButtonDefaults.outlinedButtonColors(
    containerColor = MaterialTheme.colorScheme.primary,
    contentColor = MaterialTheme.colorScheme.onPrimary
)

/**
 * Standard text field colors - completely opaque so decorations don't show through
 */
@Composable
fun standardTextFieldColors() = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
    focusedBorderColor = MaterialTheme.colorScheme.primary,
    unfocusedBorderColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.5f),
    focusedLabelColor = MaterialTheme.colorScheme.primary,
    unfocusedLabelColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
    cursorColor = MaterialTheme.colorScheme.onBackground,
    focusedTextColor = MaterialTheme.colorScheme.onBackground,
    unfocusedTextColor = MaterialTheme.colorScheme.onBackground,
    // Make backgrounds completely opaque
    focusedContainerColor = MaterialTheme.colorScheme.surface,
    unfocusedContainerColor = MaterialTheme.colorScheme.surface
)

/**
 * Back button modifier without border for cleaner visual appearance
 */
@Composable
fun backButtonModifier(
    baseModifier: Modifier = Modifier
): Modifier {
    // Remove border for cleaner visual appearance as requested by user
    return baseModifier
}

/**
 * Standard button elevation for enabled buttons
 */
@Composable
fun standardButtonElevation(): ButtonElevation = ButtonDefaults.buttonElevation(
    defaultElevation = 4.dp,
    pressedElevation = 2.dp,
    disabledElevation = 0.dp
)
