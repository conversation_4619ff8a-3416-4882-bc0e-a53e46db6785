package com.example.onelinediary

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import androidx.compose.runtime.mutableStateOf
import java.util.*

/**
 * Manages language settings for the app
 */
class LanguageManager private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: LanguageManager? = null
        
        fun getInstance(context: Context): LanguageManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: LanguageManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // Available languages
    enum class Language(val code: String, val displayName: String) {
        ENGLISH("en", "English - Engels"),
        DUTCH("nl", "Dutch - Nederlands")
    }
    
    private val sharedPreferences = context.getSharedPreferences("language_prefs", Context.MODE_PRIVATE)
    private val currentLanguageKey = "current_language"
    
    // Current language state
    val currentLanguage = mutableStateOf(getCurrentLanguage())
    
    /**
     * Get the current language from preferences
     */
    private fun getCurrentLanguage(): Language {
        val savedLanguageCode = sharedPreferences.getString(currentLanguageKey, Language.ENGLISH.code)
        return Language.values().find { it.code == savedLanguageCode } ?: Language.ENGLISH
    }
    
    /**
     * Set the current language and save to preferences
     */
    fun setLanguage(language: Language) {
        sharedPreferences.edit()
            .putString(currentLanguageKey, language.code)
            .apply()
        
        currentLanguage.value = language
        updateAppLocale(language)
    }
    
    /**
     * Update the app's locale configuration
     */
    private fun updateAppLocale(language: Language) {
        val locale = Locale(language.code)
        Locale.setDefault(locale)
        
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        
        context.resources.updateConfiguration(configuration, context.resources.displayMetrics)
    }
    
    /**
     * Get localized string resource
     */
    fun getString(context: Context, resourceId: Int): String {
        return try {
            val locale = Locale(currentLanguage.value.code)
            val configuration = Configuration(context.resources.configuration)
            configuration.setLocale(locale)
            
            val localizedContext = context.createConfigurationContext(configuration)
            localizedContext.getString(resourceId)
        } catch (e: Exception) {
            context.getString(resourceId)
        }
    }
    
    /**
     * Get localized string resource with format arguments
     */
    fun getString(context: Context, resourceId: Int, vararg formatArgs: Any): String {
        return try {
            val locale = Locale(currentLanguage.value.code)
            val configuration = Configuration(context.resources.configuration)
            configuration.setLocale(locale)
            
            val localizedContext = context.createConfigurationContext(configuration)
            localizedContext.getString(resourceId, *formatArgs)
        } catch (e: Exception) {
            context.getString(resourceId, *formatArgs)
        }
    }
    
    /**
     * Get localized resources
     */
    fun getLocalizedResources(context: Context): Resources {
        val locale = Locale(currentLanguage.value.code)
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(locale)
        
        return context.createConfigurationContext(configuration).resources
    }
    
    /**
     * Initialize language on app start
     */
    fun initializeLanguage() {
        updateAppLocale(currentLanguage.value)
    }
    
    /**
     * Get all available languages
     */
    fun getAvailableLanguages(): List<Language> {
        return Language.values().toList()
    }
    
    /**
     * Check if a language is currently selected
     */
    fun isLanguageSelected(language: Language): Boolean {
        return currentLanguage.value == language
    }
}
