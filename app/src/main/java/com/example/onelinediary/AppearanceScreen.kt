package com.example.onelinediary

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.ui.theme.*
import com.example.onelinediary.stringResource
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.ui.theme.AppTheme
import com.example.onelinediary.ui.theme.DecorationTheme

@Composable
fun AppearanceScreen(
    onBack: () -> Unit
) {
    val context = LocalContext.current
    val backgroundColor = MaterialTheme.colorScheme.background
    
    // Get the current theme
    val currentTheme = LocalAppTheme.current
    
    // Create a ThemeManager instance
    val themeManager = remember { ThemeManager.getInstance(context) }
    
    // State to track the selected theme - make it reactive to theme changes
    var selectedTheme by remember { mutableStateOf(themeManager.currentTheme.value) }
    var selectedDecorationTheme by remember { mutableStateOf(themeManager.currentDecorationTheme.value) }

    // Update selectedTheme when the actual theme changes
    LaunchedEffect(currentTheme) {
        selectedTheme = currentTheme
    }

    // Update selectedDecorationTheme when the actual decoration theme changes
    LaunchedEffect(themeManager.currentDecorationTheme.value) {
        selectedDecorationTheme = themeManager.currentDecorationTheme.value
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            // REMOVED .background(backgroundColor) to let main background show through
            .padding(16.dp),
        verticalArrangement = Arrangement.Top
    ) {
        // Top navigation row with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            Button(
                onClick = onBack,
                modifier = backButtonModifier(),
                contentPadding = standardButtonPadding,
                colors = standardButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }

            // Title
            Text(
                text = stringResource(R.string.appearance),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )

            // Spacer to balance the layout
            Spacer(modifier = Modifier.width(48.dp))
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Color Selection section
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.color_theme),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Color grid - 2 rows of colors
                val colorThemes = listOf(
                    AppTheme.STANDARD to BackgroundGreen,
                    AppTheme.BLUE to BackgroundBlue,
                    AppTheme.PINK to BackgroundPink,
                    AppTheme.PURPLE to BackgroundPurple,
                    AppTheme.YELLOW to BackgroundYellow,
                    AppTheme.ORANGE to BackgroundOrange,
                    AppTheme.RED to BackgroundRed,
                    AppTheme.TEAL to BackgroundTeal,
                    AppTheme.LAVENDER to BackgroundLavender,
                    AppTheme.BLACK_WHITE to BackgroundWhite
                )

                // First row of colors
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    colorThemes.take(5).forEach { (theme, color) ->
                        ColorSelectionButton(
                            color = color,
                            isSelected = selectedTheme == theme,
                            onClick = {
                                selectedTheme = theme
                                themeManager.setTheme(theme)
                                val themeName = when (theme) {
                                    AppTheme.STANDARD -> "Standard"
                                    AppTheme.BLACK_WHITE -> "Black & White"
                                    AppTheme.BLUE -> "Blue"
                                    AppTheme.PINK -> "Pink"
                                    AppTheme.PURPLE -> "Purple"
                                    AppTheme.YELLOW -> "Yellow"
                                    AppTheme.ORANGE -> "Orange"
                                    AppTheme.RED -> "Red"
                                    AppTheme.TEAL -> "Teal"
                                    AppTheme.LAVENDER -> "Lavender"
                                }
                                Toast.makeText(context, "$themeName theme applied", Toast.LENGTH_SHORT).show()
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Second row of colors
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    colorThemes.drop(5).forEach { (theme, color) ->
                        ColorSelectionButton(
                            color = color,
                            isSelected = selectedTheme == theme,
                            onClick = {
                                selectedTheme = theme
                                themeManager.setTheme(theme)
                                val themeName = when (theme) {
                                    AppTheme.STANDARD -> "Standard"
                                    AppTheme.BLACK_WHITE -> "Black & White"
                                    AppTheme.BLUE -> "Blue"
                                    AppTheme.PINK -> "Pink"
                                    AppTheme.PURPLE -> "Purple"
                                    AppTheme.YELLOW -> "Yellow"
                                    AppTheme.ORANGE -> "Orange"
                                    AppTheme.RED -> "Red"
                                    AppTheme.TEAL -> "Teal"
                                    AppTheme.LAVENDER -> "Lavender"
                                }
                                Toast.makeText(context, "$themeName theme applied", Toast.LENGTH_SHORT).show()
                            },
                            modifier = Modifier.weight(1f)
                        )
                    }
                    // Add empty spaces to fill the row if needed
                    repeat(5 - colorThemes.drop(5).size) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Color description
                Text(
                    text = stringResource(R.string.color_theme_description),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Theme Selection section
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = stringResource(R.string.decoration_theme),
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Theme options
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // None option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                selectedDecorationTheme = DecorationTheme.NONE
                                themeManager.setDecorationTheme(DecorationTheme.NONE)
                                Toast.makeText(context, "None theme applied", Toast.LENGTH_SHORT).show()
                            }
                            .padding(vertical = 4.dp)
                    ) {
                        RadioButton(
                            selected = selectedDecorationTheme == DecorationTheme.NONE,
                            onClick = {
                                selectedDecorationTheme = DecorationTheme.NONE
                                themeManager.setDecorationTheme(DecorationTheme.NONE)
                                Toast.makeText(context, "None theme applied", Toast.LENGTH_SHORT).show()
                            },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = MaterialTheme.colorScheme.primary,
                                unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        )
                        Text(
                            text = stringResource(R.string.none),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(start = 12.dp)
                        )
                    }

                    // Standard option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                selectedDecorationTheme = DecorationTheme.STANDARD
                                themeManager.setDecorationTheme(DecorationTheme.STANDARD)
                                Toast.makeText(context, "Standard theme applied", Toast.LENGTH_SHORT).show()
                            }
                            .padding(vertical = 4.dp)
                    ) {
                        RadioButton(
                            selected = selectedDecorationTheme == DecorationTheme.STANDARD,
                            onClick = {
                                selectedDecorationTheme = DecorationTheme.STANDARD
                                themeManager.setDecorationTheme(DecorationTheme.STANDARD)
                                Toast.makeText(context, "Standard theme applied", Toast.LENGTH_SHORT).show()
                            },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = MaterialTheme.colorScheme.primary,
                                unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        )
                        Text(
                            text = stringResource(R.string.standard),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(start = 12.dp)
                        )
                    }

                    // Romantic option
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                selectedDecorationTheme = DecorationTheme.ROMANTIC
                                themeManager.setDecorationTheme(DecorationTheme.ROMANTIC)
                                Toast.makeText(context, "Romantic theme applied", Toast.LENGTH_SHORT).show()
                            }
                            .padding(vertical = 4.dp)
                    ) {
                        RadioButton(
                            selected = selectedDecorationTheme == DecorationTheme.ROMANTIC,
                            onClick = {
                                selectedDecorationTheme = DecorationTheme.ROMANTIC
                                themeManager.setDecorationTheme(DecorationTheme.ROMANTIC)
                                Toast.makeText(context, "Romantic theme applied", Toast.LENGTH_SHORT).show()
                            },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = MaterialTheme.colorScheme.primary,
                                unselectedColor = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        )
                        Text(
                            text = stringResource(R.string.romantic),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface,
                            modifier = Modifier.padding(start = 12.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Theme description
                Text(
                    text = stringResource(R.string.decoration_theme_description),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun ColorSelectionButton(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .background(color, CircleShape)
            .border(
                width = if (isSelected) 3.dp else 1.dp,
                color = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.outline,
                shape = CircleShape
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = "Selected",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}
