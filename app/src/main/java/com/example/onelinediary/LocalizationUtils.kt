package com.example.onelinediary

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext

/**
 * Utility functions for localization
 */

/**
 * Composable function to get localized string
 */
@Composable
fun stringResource(resourceId: Int): String {
    val context = LocalContext.current
    val languageManager = LanguageManager.getInstance(context)
    return languageManager.getString(context, resourceId)
}

/**
 * Composable function to get localized string with format arguments
 */
@Composable
fun stringResource(resourceId: Int, vararg formatArgs: Any): String {
    val context = LocalContext.current
    val languageManager = LanguageManager.getInstance(context)
    return languageManager.getString(context, resourceId, *formatArgs)
}

/**
 * Extension function for Context to get localized string
 */
fun Context.getLocalizedString(resourceId: Int): String {
    val languageManager = LanguageManager.getInstance(this)
    return languageManager.getString(this, resourceId)
}

/**
 * Extension function for Context to get localized string with format arguments
 */
fun Context.getLocalizedString(resourceId: Int, vararg formatArgs: Any): String {
    val languageManager = LanguageManager.getInstance(this)
    return languageManager.getString(this, resourceId, *formatArgs)
}
